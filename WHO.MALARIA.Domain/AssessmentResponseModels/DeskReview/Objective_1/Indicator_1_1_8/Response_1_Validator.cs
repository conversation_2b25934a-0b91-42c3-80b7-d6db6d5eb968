﻿using FluentValidation;
using System;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_8
{
    /// <summary>
    /// Contains validation rules for indicator 1.1.8
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //check for other rules only of CannotBeAssessed is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleFor(x => x.HasTESBeenCarriedOut)
                .NotNull().WithMessage("Please select at least one option");

                RuleFor(x => x.CannotBeAssessedReason)
                .NotEmpty().When(x => x.HasTESBeenCarriedOut.GetValueOrDefault() == false)
                .WithMessage("Please select 'This Indicator cannot be assessed.' and provide reason");

                // Validate basic TES/iDES fields when carried out
                When(x => x.HasTESBeenCarriedOut == true, () =>
                {
                    RuleFor(x => x.NoOfSentinelSites)
                    .NotEmpty()
                    .InclusiveBetween(0, 100);

                    RuleFor(x => x.Year)
                    .NotEmpty()
                    .InclusiveBetween(2010, DateTime.Now.Year);

                    RuleFor(x => x.SummaryDetails)
                    .NotEmpty();

                    // New simplified field validations
                    RuleFor(x => x.WereTreatmentsMonitored)
                    .NotNull()
                    .WithMessage("Were treatments monitored field is required");

                    RuleFor(x => x.FailureRateOver10Reported)
                    .NotNull()
                    .WithMessage("Failure rate over 10% reported field is required");

                    RuleFor(x => x.QualityControlledStudies)
                    .NotNull()
                    .WithMessage("Quality controlled studies field is required");

                    // Conditional validation for monitored drugs
                    When(x => x.WereTreatmentsMonitored == true, () =>
                    {
                        RuleFor(x => x.MonitoredDrugs)
                        .NotEmpty()
                        .WithMessage("Monitored drugs should not be empty when treatments were monitored");
                    });

                    // Conditional validation for failure rate details
                    When(x => x.FailureRateOver10Reported == true, () =>
                    {
                        RuleFor(x => x.SitesReportedFailure)
                        .NotNull()
                        .GreaterThan(0)
                        .WithMessage("Number of sites with reported failure should be greater than 0");

                        RuleFor(x => x.FailureDrugs)
                        .NotEmpty()
                        .WithMessage("Failure drugs should not be empty when failure rate over 10% is reported");
                    });
                });
            });
        }
    }
}

