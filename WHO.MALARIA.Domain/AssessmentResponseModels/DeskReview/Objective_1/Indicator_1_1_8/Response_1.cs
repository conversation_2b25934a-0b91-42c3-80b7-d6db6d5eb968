﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.SeedingMetadata;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_8
{
    /// <summary>
    /// Contains desk review response properties for indicator 1.1.8
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public Guid StrategyId { get; set; }

        public string MetNotMetStatus { get; set; }

        public bool? HasTESBeenCarriedOut { get; set; }

        public int? NoOfSentinelSites { get; set; }

        public int? Year { get; set; }

        public string SummaryDetails { get; set; }

        // New simplified fields based on WHO recommendations
        public bool? WereTreatmentsMonitored { get; set; }

        public string MonitoredDrugs { get; set; }

        public bool? FailureRateOver10Reported { get; set; }

        public int? SitesReportedFailure { get; set; }

        public string FailureDrugs { get; set; }

        public bool? QualityControlledStudies { get; set; }

        /// <summary>
        /// Validates indicator 1.1.8
        /// </summary>
        /// <returns>Validation results for indicator 1.1.8</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        /// Get simplified analytical report response for indicator 1.1.8 based on new WHO recommendations
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>
        /// <returns>Simplified analytical output for indicator 1.1.8</returns>
        public List<object> GetSimplifiedAnalyticalResponse(Delegate translator)
        {
            var response = new List<object>();

            if (HasTESBeenCarriedOut == true)
            {
                response.Add(new {
                    Field = "TES/iDES Carried Out",
                    Value = "Yes",
                    Details = $"Sites: {NoOfSentinelSites}, Year: {Year}"
                });

                if (WereTreatmentsMonitored == true)
                {
                    response.Add(new {
                        Field = "Treatments Monitored",
                        Value = "Yes",
                        Details = MonitoredDrugs
                    });
                }

                if (FailureRateOver10Reported == true)
                {
                    response.Add(new {
                        Field = "Treatment Failure >10% Reported",
                        Value = "Yes",
                        Details = $"Sites: {SitesReportedFailure}, Drugs: {FailureDrugs}"
                    });
                }

                response.Add(new {
                    Field = "Quality Controlled Studies",
                    Value = QualityControlledStudies == true ? "Yes" : "No"
                });
            }

            return response;
        }



        /// <summary>
        /// Get analytical report response for indicator 1.1.8 based on new WHO recommendations
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>
        /// <returns>Object of analytical output indicator response dto</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator)
        {
            AnalyticalOutputType outputType = AnalyticalOutputType.Text;

            var simplifiedResponse = GetSimplifiedAnalyticalResponse(translator);

            var textResponse = new TextResponse
            {
                Content = string.Join("\n", simplifiedResponse.Select(r =>
                    $"{((dynamic)r).Field}: {((dynamic)r).Value}" +
                    (!string.IsNullOrEmpty(((dynamic)r).Details?.ToString()) ? $" - {((dynamic)r).Details}" : "")))
            };

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = textResponse
            };

            return response;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported based on new WHO recommendations
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>
        /// <param name="indicatorSequence">Contains name of indicator</param>
        /// <returns>Indicator 1.1.8 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence)
        {
            DataSet ds = new DataSet();
            DataTable dt = new DataTable();

            // Create simplified data table structure
            dt.Columns.Add("Field", typeof(string));
            dt.Columns.Add("Value", typeof(string));
            dt.Columns.Add("Details", typeof(string));

            var simplifiedResponse = GetSimplifiedAnalyticalResponse(translator);

            foreach (var item in simplifiedResponse)
            {
                var row = dt.NewRow();
                row["Field"] = ((dynamic)item).Field;
                row["Value"] = ((dynamic)item).Value;
                row["Details"] = ((dynamic)item).Details?.ToString() ?? "";
                dt.Rows.Add(row);
            }

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }
    }


}
