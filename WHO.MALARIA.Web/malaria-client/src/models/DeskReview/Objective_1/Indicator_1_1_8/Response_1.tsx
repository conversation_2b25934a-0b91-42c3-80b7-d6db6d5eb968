export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public strategyId: string | null,
    public hasTESBeenCarriedOut: boolean | null,
    public noOfSentinelSites: number | null,
    public year: number | null,
    public summaryDetails: string | null,
    // New simplified fields based on WHO recommendations
    public wereTreatmentsMonitored: boolean | null,
    public monitoredDrugs: string | null,
    public failureRateOver10Reported: boolean | null,
    public sitesReportedFailure: number | null,
    public failureDrugs: string | null,
    public qualityControlledStudies: boolean | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.strategyId = strategyId;
    this.hasTESBeenCarriedOut = hasTESBeenCarriedOut;
    this.noOfSentinelSites = noOfSentinelSites;
    this.year = year;
    this.summaryDetails = summaryDetails;
    this.wereTreatmentsMonitored = wereTreatmentsMonitored;
    this.monitoredDrugs = monitoredDrugs;
    this.failureRateOver10Reported = failureRateOver10Reported;
    this.sitesReportedFailure = sitesReportedFailure;
    this.failureDrugs = failureDrugs;
    this.qualityControlledStudies = qualityControlledStudies;
  }
  static init = (strategyId: string) =>
    new Response_1(
      false,
      null,
      strategyId,
      true,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    );
}
