﻿import { But<PERSON> } from "@mui/material";
import classNames from "classnames";
import { useEffect, useRef, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_8/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";

/** Renders the response for indicator 1.1.8 */
const Indicator_1_1_8_Response = () => {
  const location: any = useLocation();
  const strategyId: string = location?.state?.strategyId;
  const { t } = useTranslation(["indicators-responses"]);
  document.title = t(
    "indicators-responses:app:DR_Objective_1_Indicator_1_1_8_Title"
  );
  let ValidationRules: IValidationRuleProvider = {
    ...CommonValidationRules,
  };
  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onCannotBeAssessed,
    onChangeOfArrayWithIndex,
    onSave,
    onFinalize,
    onValueChange,
    getResponse,
    onChange,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(
    Response_1.init(strategyId),
    validate
  );

  const errors = useSelector((state: any) => state.error);

  useEffect(() => {
    getResponse();
  }, []);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;
  }, [response?.cannotBeAssessed]);

  //Triggers onChange of cannotBeAssessed checkbox
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current = evt.currentTarget.checked
      ? CannotBeAssessedReasonValidationRule
      : ValidationRules;

    onCannotBeAssessed(evt);
  };
  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid) {
      onFinalize();
    }
  };

  //Check condition for met and not met and return status based on new WHO criteria
  const getMetNotMetStatus = () => {
    // Not Met: If TES has not been carried out, regardless of other answers
    if (response?.hasTESBeenCarriedOut === false) {
      onValueChange("metNotMetStatus", MetNotMetEnum.NotMet);
      return;
    }

    // If TES has been carried out, check follow-up questions
    if (response?.hasTESBeenCarriedOut === true) {
      const followUpAnswers = [
        response?.wereTreatmentsMonitored,
        response?.failureRateOver10Reported === false, // "No" to failure rate is positive
        response?.qualityControlledStudies,
      ];

      // Count how many follow-up questions are answered "Yes" (or equivalent positive response)
      const positiveAnswers = followUpAnswers.filter(
        answer => answer === true
      ).length;
      const totalAnswers = followUpAnswers.filter(
        answer => answer !== null && answer !== undefined
      ).length;

      // Met: All follow-up questions answered positively
      if (positiveAnswers === 3 && totalAnswers === 3) {
        onValueChange("metNotMetStatus", MetNotMetEnum.Met);
      }
      // Partially Met: At least one but not all follow-up questions answered positively
      else if (positiveAnswers > 0 && totalAnswers > 0) {
        onValueChange("metNotMetStatus", MetNotMetEnum.PartiallyMet);
      }
      // Not Met: No positive answers or no answers provided
      else {
        onValueChange("metNotMetStatus", MetNotMetEnum.NotMet);
      }
    } else {
      // If TES question is not answered, status cannot be determined
      onValueChange("metNotMetStatus", null);
    }
  };

  useEffect(() => {
    getMetNotMetStatus();
  }, [
    response?.hasTESBeenCarriedOut,
    response?.wereTreatmentsMonitored,
    response?.failureRateOver10Reported,
    response?.qualityControlledStudies,
  ]);

  return (
    <>
      <MetNotMetStatus
        status={response.metNotMetStatus}
        tooltip={t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:MetNotMetTooltip"
        )}
      />

      <div className='response-assess-wrapper'>
        <Checkbox
          id='cannotBeAssessed'
          name='cannotBeAssessed'
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>

      {!response?.cannotBeAssessed ? (
        <div className='response-wrapper'>
          <div className='response-content'>
            <div className='row mb-3'>
              <div className='col-xs-12 col-md-6'>
                {/*Show error message if hasTESBeenCarriedOut is selected NO*/}
                {errors["hasTESBeenCarriedOut"] && (
                  <span className='Mui-error d-flex mb-2'>
                    *
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseError"
                    )}
                  </span>
                )}
                <div className='radio-wrapper'>
                  <label>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ResponseDesc"
                    )}
                  </label>
                  <RadioButtonGroup
                    id='hasTESBeenCarriedOut'
                    name='hasTESBeenCarriedOut'
                    row
                    color='primary'
                    options={[
                      new MultiSelectModel(
                        true,
                        t("indicators-responses:Common:Yes")
                      ),
                      new MultiSelectModel(
                        false,
                        t("indicators-responses:Common:No")
                      ),
                    ]}
                    value={response?.hasTESBeenCarriedOut}
                    onChange={onChange}
                  />
                </div>
              </div>

              {response?.hasTESBeenCarriedOut && (
                <>
                  {" "}
                  <div className='row my-3'>
                    <div className='col-xs-12 col-md-3'>
                      <TextBox
                        id='noOfSentinelSites'
                        name='noOfSentinelSites'
                        type='number'
                        InputLabelProps={{ shrink: true }}
                        label={t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:NoOfSentinelSites"
                        )}
                        value={response?.noOfSentinelSites}
                        inputProps={{
                          min: 0,
                          max: 100,
                          maxLength: 3,
                        }}
                        fullWidth
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          onChange(e);
                        }}
                        error={
                          errors["noOfSentinelSites"] &&
                          errors["noOfSentinelSites"]
                        }
                        helperText={
                          errors["noOfSentinelSites"] &&
                          errors["noOfSentinelSites"]
                        }
                      />
                    </div>
                    <div className='col-xs-12 col-md-3'>
                      <TextBox
                        id='year'
                        type='number'
                        name='year'
                        label={t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:Year"
                        )}
                        multiline
                        InputLabelProps={{ shrink: true }}
                        rows={1}
                        variant='outlined'
                        fullWidth
                        maxLength={4}
                        value={response?.year}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onValueChange(
                            e.currentTarget.name,
                            +e.currentTarget.value
                          )
                        }
                        error={errors["year"] && errors["year"]}
                        helperText={errors["year"] && errors["year"]}
                      />
                    </div>
                    <div className='col-xs-12 col-md-6'>
                      <TextBox
                        id='summaryDetails'
                        name='summaryDetails'
                        label={t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:ProvideSummary"
                        )}
                        multiline
                        InputLabelProps={{ shrink: true }}
                        rows={1}
                        variant='outlined'
                        fullWidth
                        value={response?.summaryDetails}
                        onChange={onChange}
                        error={
                          errors["summaryDetails"] && errors["summaryDetails"]
                        }
                        helperText={
                          errors["summaryDetails"] && errors["summaryDetails"]
                        }
                      />
                    </div>
                  </div>
                  {/* New simplified questions based on WHO recommendations */}
                  <div className='row my-3'>
                    <div className='col-xs-12'>
                      <p className='fw-bold'>
                        {t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:WereTreatmentsMonitored"
                        )}
                      </p>
                      <RadioButtonGroup
                        id='wereTreatmentsMonitored'
                        name='wereTreatmentsMonitored'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        row
                        value={response?.wereTreatmentsMonitored}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onValueChange(
                            "wereTreatmentsMonitored",
                            e.currentTarget.value === "true"
                          )
                        }
                        error={
                          errors["wereTreatmentsMonitored"] &&
                          errors["wereTreatmentsMonitored"]
                        }
                        helperText={
                          errors["wereTreatmentsMonitored"] &&
                          errors["wereTreatmentsMonitored"]
                        }
                      />
                    </div>
                  </div>
                  {response?.wereTreatmentsMonitored && (
                    <div className='row my-3'>
                      <div className='col-xs-12 col-md-6'>
                        <TextBox
                          id='monitoredDrugs'
                          name='monitoredDrugs'
                          label={t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:MonitoredDrugs"
                          )}
                          value={response?.monitoredDrugs || ""}
                          multiline
                          rows={3}
                          fullWidth
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange("monitoredDrugs", e.target.value)
                          }
                          error={
                            errors["monitoredDrugs"] && errors["monitoredDrugs"]
                          }
                          helperText={
                            errors["monitoredDrugs"] && errors["monitoredDrugs"]
                          }
                        />
                      </div>
                    </div>
                  )}
                  <div className='row my-3'>
                    <div className='col-xs-12'>
                      <p className='fw-bold'>
                        {t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:FailureRateOver10Reported"
                        )}
                      </p>
                      <RadioButtonGroup
                        id='failureRateOver10Reported'
                        name='failureRateOver10Reported'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        row
                        value={response?.failureRateOver10Reported}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onValueChange(
                            "failureRateOver10Reported",
                            e.currentTarget.value === "true"
                          )
                        }
                        error={
                          errors["failureRateOver10Reported"] &&
                          errors["failureRateOver10Reported"]
                        }
                        helperText={
                          errors["failureRateOver10Reported"] &&
                          errors["failureRateOver10Reported"]
                        }
                      />
                    </div>
                  </div>
                  {response?.failureRateOver10Reported && (
                    <>
                      <div className='row my-3'>
                        <div className='col-xs-12 col-md-3'>
                          <TextBox
                            id='sitesReportedFailure'
                            name='sitesReportedFailure'
                            type='number'
                            label={t(
                              "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:SitesReportedFailure"
                            )}
                            value={response?.sitesReportedFailure || ""}
                            inputProps={{ min: 0 }}
                            fullWidth
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) =>
                              onValueChange(
                                "sitesReportedFailure",
                                parseInt(e.target.value) || null
                              )
                            }
                            error={
                              errors["sitesReportedFailure"] &&
                              errors["sitesReportedFailure"]
                            }
                            helperText={
                              errors["sitesReportedFailure"] &&
                              errors["sitesReportedFailure"]
                            }
                          />
                        </div>
                        <div className='col-xs-12 col-md-6'>
                          <TextBox
                            id='failureDrugs'
                            name='failureDrugs'
                            label={t(
                              "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:FailureDrugs"
                            )}
                            value={response?.failureDrugs || ""}
                            multiline
                            rows={3}
                            fullWidth
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => onValueChange("failureDrugs", e.target.value)}
                            error={
                              errors["failureDrugs"] && errors["failureDrugs"]
                            }
                            helperText={
                              errors["failureDrugs"] && errors["failureDrugs"]
                            }
                          />
                        </div>
                      </div>
                    </>
                  )}
                  <div className='row my-3'>
                    <div className='col-xs-12'>
                      <p className='fw-bold'>
                        {t(
                          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_8:QualityControlledStudies"
                        )}
                      </p>
                      <RadioButtonGroup
                        id='qualityControlledStudies'
                        name='qualityControlledStudies'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        row
                        value={response?.qualityControlledStudies}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onValueChange(
                            "qualityControlledStudies",
                            e.currentTarget.value === "true"
                          )
                        }
                        error={
                          errors["qualityControlledStudies"] &&
                          errors["qualityControlledStudies"]
                        }
                        helperText={
                          errors["qualityControlledStudies"] &&
                          errors["qualityControlledStudies"]
                        }
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className='response-wrapper d-flex'>
          <TextBox
            id='cannotBeAssessedReason'
            name='cannotBeAssessedReason'
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant='outlined'
            fullWidth
            onChange={onChange}
            value={response?.cannotBeAssessedReason}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}

      <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
    </>
  );
};

export default Indicator_1_1_8_Response;
