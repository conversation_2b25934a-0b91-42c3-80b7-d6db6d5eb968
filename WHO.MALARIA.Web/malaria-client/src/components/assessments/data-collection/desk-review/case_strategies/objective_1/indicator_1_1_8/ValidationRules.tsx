import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const currentYear = new Date().getFullYear();

export const CommonValidationRules: IValidationRuleProvider = {
  hasTESBeenCarriedOut: new ValidationRuleModel(
    DataType.Boolean,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false`
  ),

  noOfSentinelSites: new ValidationRuleModel(
    DataType.Number,
    true,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && !(${Constants.Common.RootObjectNameSubstitute}.noOfSentinelSites >=0 && ${Constants.Common.RootObjectNameSubstitute}.noOfSentinelSites <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),

  year: new ValidationRuleModel(
    DataType.Number,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false &&(!${Constants.Common.RootObjectNameSubstitute}.year || !(${Constants.Common.RootObjectNameSubstitute}.year >=2010 && ${Constants.Common.RootObjectNameSubstitute}.year <=${currentYear}))`,
    "Errors.YearBetween2010ToCurrentYear"
  ),
  summaryDetails: new ValidationRuleModel(
    DataType.String,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.summaryDetails)`
  ),
  // New simplified field validations
  wereTreatmentsMonitored: new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && ${Constants.Common.RootObjectNameSubstitute}.wereTreatmentsMonitored === null`
  ),
  monitoredDrugs: new ValidationRuleModel(
    DataType.String,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && ${Constants.Common.RootObjectNameSubstitute}.wereTreatmentsMonitored === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.monitoredDrugs)`
  ),
  failureRateOver10Reported: new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && ${Constants.Common.RootObjectNameSubstitute}.failureRateOver10Reported === null`
  ),
  sitesReportedFailure: new ValidationRuleModel(
    DataType.Number,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && ${Constants.Common.RootObjectNameSubstitute}.failureRateOver10Reported === true && (${Constants.Common.RootObjectNameSubstitute}.sitesReportedFailure === null || ${Constants.Common.RootObjectNameSubstitute}.sitesReportedFailure <= 0)`
  ),
  failureDrugs: new ValidationRuleModel(
    DataType.String,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && ${Constants.Common.RootObjectNameSubstitute}.failureRateOver10Reported === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.failureDrugs)`
  ),
  qualityControlledStudies: new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && ${Constants.Common.RootObjectNameSubstitute}.qualityControlledStudies === null`
  ),
};
