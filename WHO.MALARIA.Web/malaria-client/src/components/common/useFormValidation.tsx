import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Constants } from "../../models/Constants";
import { KeyValuePair } from "../../models/DeskReview/KeyValueType";
import { DataType, ValidationAttribute } from "../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../models/ValidationRuleModel";
import { setError, removeErrorProperty } from "../../redux/ducks/error";

/**
 * Provides form validation method to validate the form data
 * @param validationRules This is configuration of the rules against which the validation will happen
 * @returns validate function
 */
const useFormValidation = (validationRules: IValidationRuleProvider) => {
    const { t } = useTranslation();
    const dispatch = useDispatch();

    //Checks if input string is null or undefined or empty and returns true or false
    const isStringNullOrEmpty = (value: string) =>
        !value || value?.trim().length === 0;

    //Checks if key or value is null or undefined or empty for the given key value pair array
    const checkForEmptyKeyValuePair = (
        keyValuePairs: Array<KeyValuePair<any, any>>
    ) => {
        let errorInKeyValuePair: Array<KeyValuePair<any, any>> = [];

        keyValuePairs.forEach((element: any) => {
            let key;
            let value;
            if (isStringNullOrEmpty(element.key?.toString())) {
                key = t("Errors.MandatoryField");
            }

            if (isStringNullOrEmpty(element.value?.toString())) {
                value = t("Errors.MandatoryField");
            }

            errorInKeyValuePair = [...errorInKeyValuePair, { key, value }];
        });

        return errorInKeyValuePair;
    };

    let pristineFormData: any;
    let isFormValid: boolean = true;

    /**
     * Perform form validation on the input
     * @param formData Data on which the validation rules will be validated.
     * @param parentObjectKey Holds the root object's key name, this is mostly useful while calling this function recursively
     */
    const validate = (formData: any, parentObjectKey: string = "") => {
        if (!pristineFormData) {
            pristineFormData = formData;
        }

        //Validate Array object
        if (Array.isArray(formData)) {
            const validationRule: ValidationRuleModel =
                validationRules[parentObjectKey];

            if (validationRule) {
                switch (validationRule.dataType) {
                    case DataType.ArrayOfKeyValuePair:
                        validateKeyValuePairArray(formData, parentObjectKey);
                        break;
                    case DataType.ArrayOfObject:
                        formData.forEach((data, index: number) => {
                            validate(data, `${parentObjectKey}[${index}]`);
                        });
                        break;
                }
            } else {
                formData.forEach((data) => {
                    validate(data, parentObjectKey);
                });
            }
        } else {
            for (const dataKey in formData) {
                /**Generate the full path for the field which will be validated based on the rule is defined in the ValidationRules object
                          for Ex. The field reside in the object as follow and when we validate for innerField then the full key path would be 
                          "step_a.field1.innerField1" and the same must be there in the ValidationRules object.
                        /**
                         * {
                         *  "step_a":{
                         *      field1:{
                         *        innerField:""
                         *     },
                         *   }
                         * }
                         */

                const fullKeyPath = parentObjectKey
                    ? `${parentObjectKey}.${dataKey}`
                    : dataKey;

                /**
                 * If validation is going to perform on the array object then validation rule must have the [{index}], and to check if
                 * the rule exists we have to replace the [0] with [{index}].
                 *
                 * for ex. validation rules has following rule "array_field[{index}].childField: { dataType:string, required: true}" and
                 * fullKeyPath has following value "array_field[0].childField", so to get the rule for childField we have to replace the
                 * [0] with [{index}] so that it will match the key and return the rule.
                 */
                const matchArraySignaturePattern = /\[[0-9]+\]/;
                const hasArraySignature = new RegExp(matchArraySignaturePattern).test(
                    fullKeyPath
                );

                const validationRule: ValidationRuleModel = hasArraySignature
                    ? validationRules[
                    fullKeyPath.replace(
                        matchArraySignaturePattern,
                        `[${Constants.Common.IndexSubstitute}]`
                    )
                    ]
                    : validationRules[fullKeyPath];

                if (validationRule) {
                    //If object is null or array object is empty then set error
                    if (
                        (formData[dataKey] === null || formData[dataKey]?.length === 0) &&
                        validationRule.required === true
                    ) {
                        dispatch(setError({
                            [fullKeyPath]: t("Errors.MandatoryField")
                        }));
                        isFormValid = false;
                        continue;
                    }
                    else {
                        dispatch(removeErrorProperty(fullKeyPath));
                    }

                    //If property of formData is an object or an array type then call the same function recursively and perform the validation for its children
                    if (validationRule.dataType === DataType.Object || validationRule.dataType === DataType.ArrayOfKeyValuePair || validationRule.dataType === DataType.ArrayOfObject) {
                        validate(formData[dataKey], fullKeyPath);
                    }
                    //validate the non object properties such as string, number etc.
                    else {
                        for (const ruleKey in validationRule) {
                            switch (ruleKey) {
                                case ValidationAttribute.Required:
                                    if (validationRule.required === true) {
                                        switch (validationRule.dataType) {
                                            case DataType.String:
                                                if (isStringNullOrEmpty(formData[dataKey])) {
                                                    dispatch(setError({
                                                        [fullKeyPath]: t("Errors.MandatoryField")
                                                    }));
                                                    isFormValid = false;
                                                } else {
                                                    dispatch(removeErrorProperty(fullKeyPath));
                                                }
                                                break;
                                            case DataType.Boolean:
                                                if (formData[dataKey] === true || formData[dataKey] === false) {
                                                    dispatch(removeErrorProperty(fullKeyPath));
                                                } else {
                                                    dispatch(setError({
                                                        [fullKeyPath]: t("Errors.MandatoryField")
                                                    }));
                                                    isFormValid = false;
                                                }
                                                break;
                                            case DataType.Number:
                                                if ([null, undefined].includes(formData[dataKey]) || isNaN(formData[dataKey])) {
                                                    dispatch(setError({
                                                        [fullKeyPath]: t("Errors.MandatoryField")
                                                    }));
                                                    isFormValid = false;
                                                } else {
                                                    dispatch(removeErrorProperty(fullKeyPath));
                                                }
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                    break;
                                case ValidationAttribute.Condition:
                                    if (validationRule.condition) {
                                        //pristineFormData - this name must be same as the object name that gets assigned at the start of this function by the formData input param
                                        let predicate: string = validationRule.condition.replaceAll(Constants.Common.RootObjectNameSubstitute, "pristineFormData");

                                        /**
                                                             * If condition has fields which are coming from array then the predicate will have array signature
                                                                as [{index}], to replace this with an actual index we are matching [{index}] using regex and 
                                                                replacing it with an index we have in fullKeyPath variable.
                                                                
                                                                for ex. condition is "array_object[{index}].numberField_1 > array_object[{index}].numberField_2"
                                                                and fullKeyPath has following string "array_object[0].numberField_1 > array_object[0].numberField_2".
                    
                                                                Inside the below If condition we are matching the index value that is zero in this example and
                                                                replacing it from the predicate.
                                                             */
                                        if (hasArraySignature) {
                                            const indexMatch = new RegExp(/(?!=\[)[0-9]+(?=\])/).exec(
                                                fullKeyPath
                                            );
                                            predicate = predicate.replace(
                                                new RegExp(`${Constants.Common.IndexSubstitute}`, "g"),
                                                indexMatch ? indexMatch[0] : ""
                                            );
                                        }

                                        // eslint-disable-next-line no-new-func
                                        const conditionFunc = new Function(
                                            'pristineFormData', 
                                            `return (${predicate})`
                                        );

                                        if (conditionFunc(pristineFormData) === true) {
                                            dispatch(
                                                setError({
                                                    [fullKeyPath]: validationRule.errorMessage
                                                        ? t(validationRule.errorMessage)
                                                        : t("Errors.MandatoryField"),
                                                })
                                            );
                                            isFormValid = false;
                                        } else {
                                            dispatch(removeErrorProperty(fullKeyPath));
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
            }
        }

        return isFormValid;
    };

    //Evaulate the condition specified for key and value
    const evaluateConditionForKeyValuePair = (
        keyValuePairs: Array<KeyValuePair<any, any>>,
        keyFieldCondition: string,
        valueFieldCondition: string,
        keyFieldErrorMessage: string,
        valueFieldErrorMessage: string
    ) => {
        let errorInKeyValuePair: Array<KeyValuePair<any, any>> = [];

        keyValuePairs.forEach((element: any) => {
            let key;
            let value;

            //The "element" word used to replace the key and value substitute has to be same as (element: any) param name of the
            //enclosing forEach loop, because at run time the condition executes on the element of the keyValuePairs.
            const keyPredicate: string = keyFieldCondition?.replaceAll(Constants.Common.KeySubstitute, "element.key");
            const valuePredicate: string = valueFieldCondition?.replaceAll(Constants.Common.ValueSubstitute, "element.value");
            if (eval(keyPredicate) == true) {
                key = t(keyFieldErrorMessage);
            }

            if (eval(valuePredicate) == true) {
                value = t(valueFieldErrorMessage);
            }

            errorInKeyValuePair = [...errorInKeyValuePair, { key, value }];
        });

        return errorInKeyValuePair;
    };

    //Validate required and conditional validation on array of key value pair
    const validateKeyValuePairArray = (formData: Array<KeyValuePair<any, any>>, parentObjectKey: string) => {
        let errorInKeyValuePair: Array<KeyValuePair<any, any>> = new Array<KeyValuePair<any, any>>();

        const keyFieldValidationRule: ValidationRuleModel = validationRules[`${parentObjectKey}.key`];
        const valueFieldValidationRule: ValidationRuleModel = validationRules[`${parentObjectKey}.value`];

        if (keyFieldValidationRule?.required || valueFieldValidationRule?.required) {
            errorInKeyValuePair = checkForEmptyKeyValuePair(formData);
        }

        //If errorInKeyValuePair is not having any error for required validation and condition is specified for either key or value
        if (errorInKeyValuePair.every((kvp: KeyValuePair<any, any>) => !kvp.key && !kvp.value) && (keyFieldValidationRule?.condition || valueFieldValidationRule?.condition)) {
            errorInKeyValuePair = evaluateConditionForKeyValuePair(
                formData,
                keyFieldValidationRule?.condition,
                valueFieldValidationRule?.condition,
                keyFieldValidationRule?.errorMessage,
                valueFieldValidationRule?.errorMessage
            )
        }

        if (
            errorInKeyValuePair.length > 0 &&
            errorInKeyValuePair.some(
                (kvp: KeyValuePair<any, any>) => kvp.key || kvp.value
            )
        ) {
            dispatch(
                setError({
                    [parentObjectKey]: errorInKeyValuePair,
                })
            );
            isFormValid = false;
        } else {
            dispatch(removeErrorProperty(parentObjectKey));
        }
    }

    return validate;
};

export default useFormValidation;
